# 本地开发环境变量配置文件
# 使用方法：在 application-local.yaml 中通过 ${ENV_VAR_NAME:default_value} 引用

# 数据库配置
DB_HOST=***************
DB_PORT=3307
DB_PASSWORD=123456
DB_USERNAME=root
DB_NAME=hmall

# Redis 配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# JWT 配置
JWT_TOKEN_TTL=2h

# 日志配置
LOG_LEVEL_ROOT=INFO
LOG_LEVEL_HMALL=DEBUG
LOG_FILE_PATH=logs/hmall-local.log

# 服务端口
SERVER_PORT=8080

# 开发环境特殊配置
SPRING_PROFILES_ACTIVE=local
DEBUG_MODE=true
